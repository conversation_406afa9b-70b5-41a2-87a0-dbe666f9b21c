import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    proxy: {
      '/api/whisper-transcribe': {
        target: 'http://localhost:4002',
        changeOrigin: true,
        secure: false
      },
      '/api': {
        target: 'http://localhost:4001',
        changeOrigin: true,
        secure: false
      },
    },
  },
})
