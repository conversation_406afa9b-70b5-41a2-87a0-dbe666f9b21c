import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import './Home.css';
import { FiMenu, FiX, FiSearch, FiUser, FiLogOut } from 'react-icons/fi';
import { auth, getUserProfile } from './firebase';
import type { UserProfile } from './firebase';
import ProfileModal from './components/ProfileModal';
import SpeechToText from './components/SpeechToText';
import PronunciationAssessment from './components/PronunciationAssessment';
import NLPAnalysis from './components/NLPAnalysis';
import IntegratedDashboard from './components/IntegratedDashboard';
import '../styles/theme.css';

const Home = () => {
  const location = useLocation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userName, setUserName] = useState('');
  const [currentView, setCurrentView] = useState<'home' | 'speech-to-text' | 'pronunciation' | 'nlp-analysis' | 'integrated-dashboard'>('home');

  const [userId, setUserId] = useState<string | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const [loadingProfile, setLoadingProfile] = useState(false);

  // Set initial view based on route
  useEffect(() => {
    if (location.pathname === '/dashboard') {
      setCurrentView('integrated-dashboard');
    }
  }, [location.pathname]);

  // Check authentication status when component mounts
  useEffect(() => {
    console.log("Checking authentication status...");
    try {
      if (!auth) {
        console.error("Firebase Auth is not initialized");
        return;
      }

      const unsubscribe = auth.onAuthStateChanged((user: any) => {
        console.log("Auth state changed:", user ? "User logged in" : "No user");
        if (user) {
          setIsAuthenticated(true);
          setUserName(user.displayName || user.email?.split('@')[0] || 'User');
          setUserId(user.uid);
        } else {
          setIsAuthenticated(false);
          setUserName('');
          setUserId(null);
          setUserProfile(null);
        }
      });

      // Cleanup subscription on unmount
      return () => unsubscribe();
    } catch (error) {
      console.error("Error in auth state change listener:", error);
    }
  }, []);

  // Fetch user profile when userId changes
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!userId) return;

      try {
        setLoadingProfile(true);
        const profile = await getUserProfile(userId);

        if (profile) {
          console.log("User profile loaded:", profile);
          setUserProfile(profile);
        } else {
          console.log("No user profile found, using default");
          // Create a default profile if none exists
          setUserProfile({
            uid: userId,
            email: auth.currentUser?.email || 'No email',
            fullName: userName || 'User',
            phoneNumber: 'Not provided',
            gender: 'Not provided',
            course: 'Not provided'
          });
        }
      } catch (error) {
        console.error('Error fetching user profile:', error);
        // Set a default profile on error
        setUserProfile({
          uid: userId,
          email: auth.currentUser?.email || 'No email',
          fullName: userName || 'User',
          phoneNumber: 'Not provided',
          gender: 'Not provided',
          course: 'Not provided'
        });
      } finally {
        setLoadingProfile(false);
      }
    };

    fetchUserProfile();
  }, [userId, userName]);

  const handleLogout = async () => {
    try {
      await auth.signOut();
      // Redirect to login page
      window.location.href = '/';
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Here you would implement the actual search functionality
    console.log('Searching for:', searchQuery);
    // For demonstration purposes, we'll just log the search query
    alert(`Searching for: ${searchQuery}`);
  };

  return (
    <div className="home-container">
      {/* Navigation Bar */}
      <nav className="navbar">
        <div className="navbar-container">
          <div className="logo">
            <h1 className="animate-fadeIn">Signify<span>.Ed</span></h1>
          </div>

          {/* Desktop Navigation */}
          <div className="desktop-nav">
            <ul className="nav-links">
              <li><button type="button" onClick={() => setCurrentView('home')} className={currentView === 'home' ? 'active' : ''}>Home</button></li>
              <li><button type="button" onClick={() => setCurrentView('integrated-dashboard')} className={currentView === 'integrated-dashboard' ? 'active' : ''}>AI Dashboard</button></li>
              <li><button type="button" onClick={() => setCurrentView('speech-to-text')} className={currentView === 'speech-to-text' ? 'active' : ''}>Speech-to-Text</button></li>
              <li><button type="button" onClick={() => setCurrentView('pronunciation')} className={currentView === 'pronunciation' ? 'active' : ''}>Pronunciation</button></li>
              <li><button type="button" onClick={() => setCurrentView('nlp-analysis')} className={currentView === 'nlp-analysis' ? 'active' : ''}>NLP Analysis</button></li>
              <li><a href="#about">About</a></li>
            </ul>

            {/* Search Bar */}
            <form className="search-container" onSubmit={handleSearch}>
              <input
                type="text"
                placeholder="Search..."
                className="search-input"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                aria-label="Search courses and materials"
              />
              {searchQuery && (
                <button
                  type="button"
                  className="search-clear-button"
                  onClick={() => setSearchQuery('')}
                  aria-label="Clear search"
                >
                  ×
                </button>
              )}
              <button type="submit" className="search-button" aria-label="Submit search">
                <FiSearch size={16} />
              </button>
            </form>

            {/* Conditional rendering based on authentication status */}
            {isAuthenticated ? (
              <div className="profile-container">
                <button
                  type="button"
                  className="profile-button"
                  onClick={() => setIsProfileModalOpen(true)}
                >
                  <FiUser size={16} />
                  <span>{userName}</span>
                </button>
                <button type="button" className="logout-button" onClick={handleLogout}>
                  <FiLogOut size={16} />
                </button>
              </div>
            ) : (
              <button
                type="button"
                className="cta-button"
                onClick={() => { window.location.href = '/'; }}
              >
                Get Started
              </button>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="mobile-menu-button">
            <button type="button" onClick={() => setMobileMenuOpen(!mobileMenuOpen)}>
              {mobileMenuOpen ? <FiX size={24} /> : <FiMenu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="mobile-nav">
            {/* Mobile Search Bar */}
            <form className="mobile-search-container" onSubmit={handleSearch}>
              <input
                type="text"
                placeholder="Search courses, materials..."
                className="mobile-search-input"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                aria-label="Search courses and materials"
              />
              {searchQuery && (
                <button
                  type="button"
                  className="mobile-search-clear-button"
                  onClick={() => setSearchQuery('')}
                  aria-label="Clear search"
                >
                  ×
                </button>
              )}
              <button type="submit" className="mobile-search-button" aria-label="Submit search">
                <FiSearch size={18} />
              </button>
            </form>

            <ul className="mobile-nav-links">
              <li><button type="button" onClick={() => { setCurrentView('home'); setMobileMenuOpen(false); }} className={currentView === 'home' ? 'active' : ''}>Home</button></li>
              <li><button type="button" onClick={() => { setCurrentView('integrated-dashboard'); setMobileMenuOpen(false); }} className={currentView === 'integrated-dashboard' ? 'active' : ''}>AI Dashboard</button></li>
              <li><button type="button" onClick={() => { setCurrentView('speech-to-text'); setMobileMenuOpen(false); }} className={currentView === 'speech-to-text' ? 'active' : ''}>Speech-to-Text</button></li>
              <li><button type="button" onClick={() => { setCurrentView('pronunciation'); setMobileMenuOpen(false); }} className={currentView === 'pronunciation' ? 'active' : ''}>Pronunciation</button></li>
              <li><button type="button" onClick={() => { setCurrentView('nlp-analysis'); setMobileMenuOpen(false); }} className={currentView === 'nlp-analysis' ? 'active' : ''}>NLP Analysis</button></li>
              <li><a href="#about">About</a></li>
            </ul>

            {/* Conditional rendering based on authentication status */}
            {isAuthenticated ? (
              <div className="mobile-profile-container">
                <button
                  type="button"
                  className="mobile-profile-info"
                  onClick={() => setIsProfileModalOpen(true)}
                  style={{
                    cursor: 'pointer',
                    background: 'none',
                    border: 'none',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.8rem',
                    width: '100%',
                    textAlign: 'left',
                    color: 'inherit'
                  }}
                >
                  <FiUser size={18} />
                  <span>{userName}</span>
                </button>
                <button type="button" className="mobile-logout-button" onClick={handleLogout}>
                  <FiLogOut size={18} />
                  <span>Logout</span>
                </button>
              </div>
            ) : (
              <button
                type="button"
                className="mobile-cta-button"
                onClick={() => { window.location.href = '/'; }}
              >
                Get Started
              </button>
            )}
          </div>
        )}
      </nav>

      {/* Main Content */}
      <main>
        {currentView === 'home' && (
          <>
            {/* Hero Section */}
            <section className="hero-section">
              <div className="hero-content">
                <h1 className="hero-title animate-fadeInUp">Signify.Ed Learning Platform</h1>
                <div className="hero-description">
                  <p className="animate-fadeInUp animate-delay-1">
                    Welcome to Signify.Ed, your comprehensive learning platform featuring advanced AI-powered tools
                    for education and accessibility. Our platform includes speech-to-text conversion, pronunciation
                    assessment, and interactive learning modules designed to enhance your educational experience.
                  </p>
                  <p className="animate-fadeInUp animate-delay-2">
                    Explore our cutting-edge features including our custom-trained speech recognition model,
                    pronunciation assessment tools, and comprehensive course materials.
                  </p>
                </div>
              </div>
              <div className="hero-image animate-fadeInRight animate-delay-1">
                <div className="image-container">
                  <div className="image-placeholder">
                    <div style={{
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                      color: '#0a192f',
                      fontWeight: 'bold',
                      fontSize: '1.5rem'
                    }}>
                      🎓 AI Learning
                    </div>
                  </div>
                </div>
              </div>
            </section>

            {/* Features Section */}
            <section className="courses-section">
              <h2 className="animate-fadeInUp">AI-Powered Learning Features</h2>
              <div className="course-cards">
                <div className="course-card animate-fadeInUp animate-delay-1" style={{ background: 'linear-gradient(135deg, rgba(255, 204, 0, 0.1), rgba(16, 185, 129, 0.1))', border: '2px solid rgba(255, 204, 0, 0.3)' }}>
                  <div className="course-image">
                    <div style={{
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                      color: '#ffcc00',
                      fontSize: '3rem',
                      zIndex: 2
                    }}>
                      🚀
                    </div>
                  </div>
                  <div className="course-content">
                    <h3>AI Dashboard</h3>
                    <p>Unified platform combining speech-to-text, NLP analysis, pronunciation assessment, and learning analytics in one powerful interface.</p>
                    <button type="button" className="course-button" onClick={() => setCurrentView('integrated-dashboard')} style={{ background: 'linear-gradient(135deg, #ffcc00, #10b981)', color: '#0a192f' }}>Launch Dashboard</button>
                  </div>
                </div>
                <div className="course-card animate-fadeInUp animate-delay-2">
                  <div className="course-image">
                    <div style={{
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                      color: '#ffffff',
                      fontSize: '3rem',
                      zIndex: 2
                    }}>
                      🎤
                    </div>
                  </div>
                  <div className="course-content">
                    <h3>Speech-to-Text AI</h3>
                    <p>Convert speech to text using our custom-trained AI model. Perfect for transcription, note-taking, and accessibility.</p>
                    <button type="button" className="course-button" onClick={() => setCurrentView('speech-to-text')}>Try Now</button>
                  </div>
                </div>
                <div className="course-card animate-fadeInUp animate-delay-3">
                  <div className="course-image">
                    <div style={{
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                      color: '#ffffff',
                      fontSize: '3rem',
                      zIndex: 2
                    }}>
                      🗣️
                    </div>
                  </div>
                  <div className="course-content">
                    <h3>Pronunciation Assessment</h3>
                    <p>Improve your pronunciation with AI-powered feedback and scoring. Practice speaking and get instant results.</p>
                    <button type="button" className="course-button" onClick={() => setCurrentView('pronunciation')}>Practice Now</button>
                  </div>
                </div>
                <div className="course-card animate-fadeInUp animate-delay-4">
                  <div className="course-image">
                    <div style={{
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                      color: '#ffffff',
                      fontSize: '3rem',
                      zIndex: 2
                    }}>
                      🧠
                    </div>
                  </div>
                  <div className="course-content">
                    <h3>NLP Text Analysis</h3>
                    <p>Advanced natural language processing for sentiment analysis, entity extraction, and text insights.</p>
                    <button type="button" className="course-button" onClick={() => setCurrentView('nlp-analysis')}>Analyze Now</button>
                  </div>
                </div>
              </div>
            </section>
          </>
        )}

        {currentView === 'integrated-dashboard' && <IntegratedDashboard />}
        {currentView === 'speech-to-text' && <SpeechToText />}
        {currentView === 'pronunciation' && <PronunciationAssessment />}
        {currentView === 'nlp-analysis' && <NLPAnalysis />}
      </main>

      {/* Profile Modal */}
      <ProfileModal
        isOpen={isProfileModalOpen}
        onClose={() => setIsProfileModalOpen(false)}
        userProfile={userProfile}
        loading={loadingProfile}
      />

      {/* Footer */}
      <footer className="footer">
        <div className="footer-content">
          <div className="footer-logo">
            <h2>Signify<span>.Ed</span></h2>
          </div>
          <div className="footer-links">
            <div className="footer-column">
              <h3>Programs</h3>
              <ul>
                <li><a href="#courses">Courses</a></li>
                <li><a href="#study-material">Study Material</a></li>
                <li><a href="#video-to-isl">Video to ISL Converter</a></li>
              </ul>
            </div>
            <div className="footer-column">
              <h3>Company</h3>
              <ul>
                <li><a href="#about">About Us</a></li>
                <li><a href="#careers">Careers</a></li>
                <li><a href="#contact">Contact</a></li>
              </ul>
            </div>
            <div className="footer-column">
              <h3>Resources</h3>
              <ul>
                <li><a href="#blog">Blog</a></li>
                <li><a href="#guides">Guides</a></li>
                <li><a href="#faq">FAQ</a></li>
              </ul>
            </div>
          </div>
        </div>
        <div className="footer-bottom">
          <p>&copy; {new Date().getFullYear()} Signify.Ed. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default Home;