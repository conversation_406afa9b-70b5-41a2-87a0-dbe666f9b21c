// backend/server.js
require('dotenv').config();
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');


const app = express();
const PORT = process.env.PORT || 4001;

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = './uploads';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /wav|mp3|flac|m4a|webm|ogg/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype) || file.mimetype.startsWith('audio/');

    console.log('File upload check:', {
      originalname: file.originalname,
      mimetype: file.mimetype,
      extname: extname,
      mimetypeMatch: mimetype
    });

    if (mimetype || extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only audio files are allowed!'));
    }
  }
});





app.use(cors());
app.use(bodyParser.json());
app.use('/uploads', express.static('uploads'));

// In-memory user store (for demo only)
const users = [];

// In-memory storage for training data and models (for demo)
const trainingData = [];
const models = [
  {
    id: 'ljspeech_model',
    name: 'LJSpeech Trained Model',
    accuracy: 85,
    trainingData: 13100,
    status: 'deployed',
    createdAt: new Date().toISOString()
  }
];

// Helper function to call Python speech-to-text script with optional NLP
function transcribeWithCustomModelAndNLP(audioPath, includeNlp = false) {
  return new Promise((resolve, reject) => {
    const pythonScript = path.join(__dirname, '../ai-training/signify_integration.py');
    const python = spawn('python', ['-c', `
import sys
import json
sys.path.append('${path.join(__dirname, '../ai-training').replace(/\\/g, '/')}')
from signify_integration import transcribe_audio
import asyncio

async def main():
    result = await transcribe_audio('${audioPath.replace(/\\/g, '/')}', include_nlp=${includeNlp ? 'True' : 'False'})
    if result['success']:
        output = {
            'text': result['transcription'],
            'model': 'custom',
            'confidence': 0.85,
            'word_count': result.get('word_count', 0),
            'timestamp': result.get('timestamp'),
            'nlp_analysis': result.get('nlp_analysis')
        }
        print(json.dumps(output))
    else:
        print('ERROR: ' + result.get('error', 'Unknown error'))

asyncio.run(main())
    `]);

    let output = '';
    let error = '';

    python.stdout.on('data', (data) => {
      output += data.toString();
    });

    python.stderr.on('data', (data) => {
      error += data.toString();
    });

    python.on('close', (code) => {
      if (code === 0 && !output.startsWith('ERROR:')) {
        try {
          const result = JSON.parse(output.trim());
          resolve(result);
        } catch (parseError) {
          // Fallback for simple text output
          resolve({
            text: output.trim(),
            model: 'custom',
            confidence: 0.85
          });
        }
      } else {
        reject(new Error(error || output));
      }
    });
  });
}

// Legacy function for backward compatibility
function transcribeWithCustomModel(audioPath) {
  return transcribeWithCustomModelAndNLP(audioPath, false).then(result => result.text);
}

// Helper function for NLP analysis of text
function analyzeTextWithNLP(text) {
  return new Promise((resolve, reject) => {
    const python = spawn('python', ['-c', `
import sys
import json
sys.path.append('${path.join(__dirname, '../ai-training').replace(/\\/g, '/')}')
from nlp_processor import process_speech_to_nlp
import asyncio

async def main():
    try:
        result = await process_speech_to_nlp('${text.replace(/'/g, "\\'")}', include_advanced=True)
        print(json.dumps(result))
    except Exception as e:
        print(json.dumps({"error": str(e)}))

asyncio.run(main())
    `]);

    let output = '';
    let error = '';

    python.stdout.on('data', (data) => {
      output += data.toString();
    });

    python.stderr.on('data', (data) => {
      error += data.toString();
    });

    python.on('close', (code) => {
      try {
        const result = JSON.parse(output.trim());
        if (result.error) {
          reject(new Error(result.error));
        } else {
          resolve(result);
        }
      } catch (parseError) {
        reject(new Error(error || parseError.message));
      }
    });
  });
}

// Helper function to calculate unified score
function calculateUnifiedScore(results) {
  let scores = [];

  // Add transcription confidence if available
  if (results.confidence) {
    scores.push(results.confidence * 100);
  }

  // Add NLP confidence if available
  if (results.nlp_analysis && results.nlp_analysis.confidence) {
    scores.push(results.nlp_analysis.confidence * 100);
  }

  // Add pronunciation accuracy if available
  if (results.pronunciation && results.pronunciation.accuracy) {
    scores.push(results.pronunciation.accuracy);
  }

  // If we have transcription but no specific scores, give base score
  if (results.transcription && scores.length === 0) {
    scores.push(80); // Base score for successful transcription
  }

  // Calculate average score
  if (scores.length > 0) {
    return Math.round(scores.reduce((a, b) => a + b, 0) / scores.length);
  }

  return 0;
}

// Speech-to-Text API Endpoints

// Transcribe audio with custom trained model
app.post('/api/custom-transcribe', upload.single('audio'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No audio file provided' });
    }

    const includeNlp = req.body.includeNlp === 'true' || req.body.includeNlp === true;
    console.log('Transcribing with custom model:', req.file.path, 'Include NLP:', includeNlp);

    const result = await transcribeWithCustomModelAndNLP(req.file.path, includeNlp);

    // Clean up uploaded file
    fs.unlinkSync(req.file.path);

    res.json(result);
  } catch (error) {
    console.error('Custom transcription error:', error);

    // Clean up uploaded file if it exists
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      error: 'Failed to transcribe audio with custom model',
      details: error.message
    });
  }
});

// Get training data
app.get('/api/training-data', (req, res) => {
  res.json(trainingData);
});

// Add training data
app.post('/api/training-data', upload.single('audio'), (req, res) => {
  try {
    if (!req.file || !req.body.transcription) {
      return res.status(400).json({ error: 'Audio file and transcription are required' });
    }

    const newData = {
      id: Date.now().toString(),
      audioFile: req.file,
      transcription: req.body.transcription,
      duration: 0, // Would need audio analysis to get real duration
      status: 'validated'
    };

    trainingData.push(newData);
    res.json(newData);
  } catch (error) {
    console.error('Error adding training data:', error);
    res.status(500).json({ error: 'Failed to add training data' });
  }
});

// Get models
app.get('/api/models', (req, res) => {
  res.json(models);
});

// Start training (mock endpoint)
app.post('/api/start-training', (req, res) => {
  console.log('Training started with data IDs:', req.body.trainingDataIds);
  res.json({ message: 'Training started', jobId: Date.now().toString() });
});

// Get training progress (mock endpoint)
app.get('/api/training-progress', (req, res) => {
  res.json({ percentage: 100 }); // Always return 100% since model is already trained
});

// Unified analysis endpoint - combines speech-to-text, NLP, and pronunciation
app.post('/api/unified-analysis', upload.single('audio'), async (req, res) => {
  try {
    const { text, expectedText, includeNlp = true, includePronunciation = false } = req.body;
    let results = {};

    // If audio file is provided, process it
    if (req.file) {
      console.log('Processing audio for unified analysis:', req.file.path);

      // Speech-to-text with NLP
      const speechResult = await transcribeWithCustomModelAndNLP(req.file.path, includeNlp === 'true' || includeNlp === true);
      results.transcription = speechResult.text;
      results.nlp_analysis = speechResult.nlp_analysis;
      results.confidence = speechResult.confidence;

      // Pronunciation assessment if expected text is provided
      if (expectedText && includePronunciation === 'true') {
        try {
          const pronResult = await assessPronunciation(req.file.path, expectedText);
          results.pronunciation = pronResult;
        } catch (pronError) {
          console.error('Pronunciation assessment error:', pronError);
          results.pronunciation = { error: 'Pronunciation assessment failed' };
        }
      }

      // Clean up uploaded file
      fs.unlinkSync(req.file.path);
    }
    // If only text is provided, do NLP analysis
    else if (text) {
      console.log('Processing text for NLP analysis');

      if (includeNlp === 'true') {
        const nlpResult = await analyzeTextWithNLP(text);
        results.nlp_analysis = nlpResult;
      }
    }

    // Calculate overall score
    results.overall_score = calculateUnifiedScore(results);
    results.timestamp = new Date().toISOString();
    results.processing_time = Date.now() - req.startTime;

    res.json(results);

  } catch (error) {
    console.error('Unified analysis error:', error);

    // Clean up uploaded file if it exists
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      error: 'Failed to perform unified analysis',
      details: error.message
    });
  }
});

// User progress endpoint (mock data for now)
app.get('/api/user-progress/:userId?', (req, res) => {
  const userId = req.params.userId || 'default';

  // Mock user progress data
  const mockProgress = {
    user_id: userId,
    total_sessions: Math.floor(Math.random() * 100) + 20,
    average_score: Math.floor(Math.random() * 30) + 70,
    improvement_rate: Math.floor(Math.random() * 20) + 5,
    streak_days: Math.floor(Math.random() * 14) + 1,
    favorite_topics: ['Technology', 'Education', 'Science', 'Business'],
    weak_areas: ['Pronunciation', 'Complex Sentences', 'Technical Terms'],
    achievements: [
      'First Session',
      '7-Day Streak',
      'High Accuracy',
      'NLP Explorer',
      'Pronunciation Master'
    ],
    recent_activity: [
      {
        date: new Date(Date.now() - 86400000).toISOString(),
        sessions: 3,
        average_score: 85
      },
      {
        date: new Date(Date.now() - 172800000).toISOString(),
        sessions: 2,
        average_score: 78
      }
    ]
  };

  res.json(mockProgress);
});

// Session history endpoint (mock data for now)
app.get('/api/session-history/:userId?', (req, res) => {
  const userId = req.params.userId || 'default';
  const limit = parseInt(req.query.limit) || 10;

  // Mock session history
  const mockSessions = [];
  for (let i = 0; i < limit; i++) {
    const sessionTypes = ['speech-to-text', 'pronunciation', 'nlp-analysis', 'unified'];
    const randomType = sessionTypes[Math.floor(Math.random() * sessionTypes.length)];

    mockSessions.push({
      id: `session_${Date.now()}_${i}`,
      user_id: userId,
      timestamp: new Date(Date.now() - (i * 3600000)).toISOString(),
      type: randomType,
      input: randomType === 'speech-to-text' ? 'Audio recording' : 'Text input for analysis',
      results: {
        transcription: randomType.includes('speech') ? 'Sample transcription text' : null,
        nlp_analysis: {
          sentiment: { label: 'positive', score: 0.8 },
          topics: ['Technology', 'Education']
        },
        pronunciation: randomType === 'pronunciation' ? { accuracy: Math.floor(Math.random() * 30) + 70 } : null
      },
      score: Math.floor(Math.random() * 30) + 70,
      duration: Math.floor(Math.random() * 120) + 30
    });
  }

  res.json(mockSessions);
});

// NLP Analysis endpoint for text
app.post('/api/analyze-text', async (req, res) => {
  try {
    const { text, includeAdvanced = true } = req.body;

    if (!text || typeof text !== 'string') {
      return res.status(400).json({ error: 'Text is required' });
    }

    console.log('Analyzing text with NLP:', text.substring(0, 100) + '...');

    const python = spawn('python', ['-c', `
import sys
import json
sys.path.append('${path.join(__dirname, '../ai-training').replace(/\\/g, '/')}')
from nlp_processor import process_speech_to_nlp
import asyncio

async def main():
    try:
        result = await process_speech_to_nlp('${text.replace(/'/g, "\\'")}', include_advanced=${includeAdvanced ? 'True' : 'False'})
        print(json.dumps(result))
    except Exception as e:
        print(json.dumps({"error": str(e)}))

asyncio.run(main())
    `]);

    let output = '';
    let error = '';

    python.stdout.on('data', (data) => {
      output += data.toString();
    });

    python.stderr.on('data', (data) => {
      error += data.toString();
    });

    python.on('close', (code) => {
      try {
        const result = JSON.parse(output.trim());
        if (result.error) {
          res.status(500).json({ error: 'NLP analysis failed', details: result.error });
        } else {
          res.json(result);
        }
      } catch (parseError) {
        console.error('NLP analysis error:', error || parseError);
        res.status(500).json({
          error: 'Failed to analyze text',
          details: error || parseError.message
        });
      }
    });

  } catch (error) {
    console.error('NLP endpoint error:', error);
    res.status(500).json({
      error: 'Failed to process NLP request',
      details: error.message
    });
  }
});

// ASL Animation Generation endpoint
app.post('/api/generate-asl', async (req, res) => {
  try {
    const { text, animation_settings = {} } = req.body;

    if (!text || typeof text !== 'string') {
      return res.status(400).json({ error: 'Text is required for ASL generation' });
    }

    console.log('Generating ASL animation for text:', text.substring(0, 100) + '...');

    // Call Python script for text-to-ASL conversion
    const python = spawn('python', ['-c', `
import sys
import json
import os
sys.path.append('${path.join(__dirname, '../../').replace(/\\/g, '/')}')

try:
    from text_to_3d_asl import TextTo3DASL
    from pose_to_blender_converter import PoseToBlenderConverter

    # Initialize the pipeline
    pipeline = TextTo3DASL()
    converter = PoseToBlenderConverter()

    # Generate pose sequence
    if not pipeline.load_model():
        print(json.dumps({"error": "Failed to load ASL model"}))
        sys.exit(1)

    pose_sequence = pipeline.generate_pose_sequence('${text.replace(/'/g, "\\'")}', max_length=${animation_settings.max_frames || 100})

    if pose_sequence is None:
        print(json.dumps({"error": "Failed to generate pose sequence"}))
        sys.exit(1)

    # Convert to animation data
    animation_data = converter.create_blender_animation_data(
        pose_sequence,
        frame_rate=${animation_settings.frame_rate || 24}
    )

    # Apply smoothing
    if ${animation_settings.smoothing || 0.3} > 0:
        animation_data = converter.smooth_animation(
            animation_data,
            smoothing_factor=${animation_settings.smoothing || 0.3}
        )

    # Add metadata
    animation_data['metadata'] = {
        'source_text': '${text.replace(/'/g, "\\'")}',
        'source': 'signify_ed_api',
        'generated_at': '${new Date().toISOString()}',
        'model_version': 'v1.0'
    }

    print(json.dumps(animation_data))

except Exception as e:
    import traceback
    error_details = traceback.format_exc()
    print(json.dumps({
        "error": f"ASL generation failed: {str(e)}",
        "details": error_details
    }))
    `]);

    let output = '';
    let error = '';

    python.stdout.on('data', (data) => {
      output += data.toString();
    });

    python.stderr.on('data', (data) => {
      error += data.toString();
    });

    python.on('close', (code) => {
      try {
        const result = JSON.parse(output.trim());
        if (result.error) {
          console.error('ASL generation error:', result.error);
          res.status(500).json({
            error: 'Failed to generate ASL animation',
            details: result.error,
            debug_info: result.details
          });
        } else {
          // Add processing metadata
          result.processing_info = {
            processing_time: Date.now() - req.startTime,
            api_version: '1.0',
            backend_version: 'signify_ed_v1'
          };

          res.json(result);
        }
      } catch (parseError) {
        console.error('ASL generation parse error:', error || parseError);
        res.status(500).json({
          error: 'Failed to process ASL generation response',
          details: error || parseError.message,
          raw_output: output.substring(0, 500)
        });
      }
    });

  } catch (error) {
    console.error('ASL generation endpoint error:', error);
    res.status(500).json({
      error: 'Failed to process ASL generation request',
      details: error.message
    });
  }
});

// ASL Animation Status endpoint
app.get('/api/asl-status', (req, res) => {
  res.json({
    status: 'available',
    features: {
      text_to_asl: true,
      pose_generation: true,
      animation_export: true,
      real_time_preview: true
    },
    supported_formats: ['json', 'blender'],
    max_text_length: 500,
    max_animation_duration: 300, // seconds
    available_models: ['signify_ed_v1']
  });
});

// Pronunciation assessment endpoint
app.post('/api/assess-pronunciation', upload.single('audio'), async (req, res) => {
  try {
    if (!req.file || !req.body.expectedText) {
      return res.status(400).json({ error: 'Audio file and expected text are required' });
    }

    console.log('Assessing pronunciation:', req.file.path);
    const transcription = await transcribeWithCustomModel(req.file.path);

    // Simple accuracy calculation (you could enhance this)
    const expected = req.body.expectedText.toLowerCase().trim();
    const actual = transcription.toLowerCase().trim();

    const expectedWords = expected.split(/\s+/);
    const actualWords = actual.split(/\s+/);

    let correctWords = 0;
    const maxLength = Math.max(expectedWords.length, actualWords.length);

    for (let i = 0; i < Math.min(expectedWords.length, actualWords.length); i++) {
      if (expectedWords[i] === actualWords[i]) {
        correctWords++;
      }
    }

    const accuracy = maxLength > 0 ? (correctWords / maxLength) * 100 : 0;

    // Clean up uploaded file
    fs.unlinkSync(req.file.path);

    res.json({
      transcription: transcription,
      expectedText: req.body.expectedText,
      accuracy: Math.round(accuracy),
      feedback: accuracy >= 80 ? 'Excellent pronunciation!' :
                accuracy >= 60 ? 'Good, but could be improved' :
                'Needs more practice'
    });
  } catch (error) {
    console.error('Pronunciation assessment error:', error);

    // Clean up uploaded file if it exists
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      error: 'Failed to assess pronunciation',
      details: error.message
    });
  }
});

app.post('/api/signup', (req, res) => {
  const { email, password } = req.body;
  if (!email || !password) {
    return res.status(400).json({ error: 'Email and password are required.' });
  }
  if (users.find(u => u.email === email)) {
    return res.status(409).json({ error: 'User already exists.' });
  }
  users.push({ email, password });
  res.json({ message: 'Signup successful.' });
});

app.post('/api/login', (req, res) => {
  const { email, password, rememberMe } = req.body;
  const user = users.find(u => u.email === email && u.password === password);
  if (!user) {
    return res.status(401).json({ error: 'Invalid credentials.' });
  }

  // In a real app, you would create a session or JWT token here
  // For demo purposes, we'll just acknowledge the rememberMe flag
  const sessionDuration = rememberMe ? '30 days' : '1 hour';
  res.json({
    message: 'Login successful.',
    sessionDuration,
    user: { email: user.email }
  });
});



app.listen(PORT, () => {
  console.log(`Auth server running on http://localhost:${PORT}`);
});
