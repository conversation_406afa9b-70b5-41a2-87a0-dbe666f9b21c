"""
AI Training Server for Speech-to-Text Models
Provides APIs for training custom speech recognition models
"""

import os
import uuid
from datetime import datetime
from typing import List, Optional
from pathlib import Path
from contextlib import asynccontextmanager

from fastapi import FastAPI, File, UploadFile, Form, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

from database import Database, TrainingData, Model
from trainer import SpeechTrainer
from audio_processor import AudioProcessor

# Initialize components
db = Database()
trainer = SpeechTrainer()
audio_processor = AudioProcessor()

@asynccontextmanager
async def lifespan(app_instance: FastAPI):
    # Startup
    await db.init_db()

    # Load any deployed models
    try:
        models = await db.get_all_models()
        deployed_models = [m for m in models if m.status == "deployed"]

        if deployed_models:
            # Load the first deployed model
            deployed_model = deployed_models[0]
            print(f"🚀 Loading deployed model: {deployed_model.name}")
            await trainer.load_model_for_inference(deployed_model.id)
            print(f"✅ Model {deployed_model.name} loaded successfully!")
        else:
            print("⚠️ No deployed models found")
    except Exception as e:
        print(f"❌ Error loading deployed model: {e}")

    yield
    # Shutdown
    await db.close()

app = FastAPI(title="AI Speech Training API", version="1.0.0", lifespan=lifespan)

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5174", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global training state
training_state = {
    "is_training": False,
    "progress": 0,
    "current_model_id": None
}

class TrainingRequest(BaseModel):
    training_data_ids: List[str]
    model_name: Optional[str] = None
    epochs: Optional[int] = 10
    learning_rate: Optional[float] = 1e-4

class ModelResponse(BaseModel):
    id: str
    name: str
    accuracy: float
    training_data: int
    status: str
    created_at: str

@app.get("/")
async def root():
    return {"message": "AI Speech Training API", "status": "running"}

@app.get("/api/training-data")
async def get_training_data():
    """Get all training data samples"""
    try:
        data = await db.get_all_training_data()
        return [
            {
                "id": item.id,
                "transcription": item.transcription,
                "duration": item.duration,
                "status": item.status,
                "created_at": item.created_at.isoformat()
            }
            for item in data
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/training-data")
async def add_training_data(
    audio: UploadFile = File(...),
    transcription: str = Form(...)
):
    """Add new training data sample"""
    try:
        # Validate audio file
        if not audio.content_type.startswith('audio/'):
            raise HTTPException(status_code=400, detail="File must be an audio file")

        # Generate unique ID
        data_id = str(uuid.uuid4())

        # Save audio file
        audio_dir = Path("data/audio")
        audio_dir.mkdir(parents=True, exist_ok=True)
        audio_path = audio_dir / f"{data_id}.wav"

        # Read and process audio
        audio_content = await audio.read()
        duration = await audio_processor.save_and_process_audio(
            audio_content, audio_path
        )

        # Save to database
        training_data = TrainingData(
            id=data_id,
            audio_path=str(audio_path),
            transcription=transcription,
            duration=duration,
            status="pending"
        )

        await db.add_training_data(training_data)

        return {
            "id": data_id,
            "transcription": transcription,
            "duration": duration,
            "status": "pending"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/api/training-data/{data_id}/validate")
async def validate_training_data(data_id: str):
    """Mark training data as validated"""
    try:
        await db.update_training_data_status(data_id, "validated")
        return {"message": "Training data validated"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/start-training")
async def start_training(
    request: TrainingRequest,
    background_tasks: BackgroundTasks
):
    """Start training a new model"""
    try:
        if training_state["is_training"]:
            raise HTTPException(status_code=400, detail="Training already in progress")

        # Validate training data
        if len(request.training_data_ids) < 10:
            raise HTTPException(
                status_code=400,
                detail="At least 10 validated samples required for training"
            )

        # Create new model record
        model_id = str(uuid.uuid4())
        model_name = request.model_name or f"model_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        model = Model(
            id=model_id,
            name=model_name,
            status="training",
            accuracy=0.0,
            training_data_count=len(request.training_data_ids)
        )

        await db.add_model(model)

        # Start training in background
        training_state["is_training"] = True
        training_state["progress"] = 0
        training_state["current_model_id"] = model_id

        background_tasks.add_task(
            run_training,
            model_id,
            request.training_data_ids,
            request.epochs,
            request.learning_rate
        )

        return {"message": "Training started", "model_id": model_id}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/training-progress")
async def get_training_progress():
    """Get current training progress"""
    return {
        "is_training": training_state["is_training"],
        "percentage": training_state["progress"],
        "model_id": training_state["current_model_id"]
    }

@app.get("/api/models")
async def get_models():
    """Get all trained models"""
    try:
        models = await db.get_all_models()
        return [
            {
                "id": model.id,
                "name": model.name,
                "accuracy": model.accuracy,
                "trainingData": model.training_data_count,
                "status": model.status,
                "createdAt": model.created_at.isoformat()
            }
            for model in models
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/models/{model_id}/deploy")
async def deploy_model(model_id: str):
    """Deploy a trained model for inference"""
    try:
        # Undeploy any currently deployed models
        await db.undeploy_all_models()

        # Deploy the selected model
        await db.update_model_status(model_id, "deployed")

        # Load model for inference
        await trainer.load_model_for_inference(model_id)

        return {"message": "Model deployed successfully"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/custom-transcribe")
async def custom_transcribe(audio: UploadFile = File(...)):
    """Transcribe audio using custom trained model"""
    try:
        if not trainer.inference_model:
            raise HTTPException(status_code=400, detail="No model deployed for inference")

        # Process audio
        audio_content = await audio.read()
        temp_path = f"temp_{uuid.uuid4()}.wav"

        try:
            await audio_processor.save_and_process_audio(audio_content, temp_path)

            # Transcribe using custom model
            transcription = await trainer.transcribe_audio(temp_path)

            return {"text": transcription, "success": True}

        finally:
            # Clean up temp file
            if os.path.exists(temp_path):
                os.remove(temp_path)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def run_training(
    model_id: str,
    training_data_ids: List[str],
    epochs: int,
    learning_rate: float
):
    """Background task for model training"""
    try:
        # Get training data
        training_data = await db.get_training_data_by_ids(training_data_ids)

        # Prepare training dataset
        dataset = await trainer.prepare_dataset(training_data)

        # Train model with progress updates
        async def progress_callback(progress: float):
            training_state["progress"] = int(progress * 100)

        accuracy = await trainer.train_model(
            dataset,
            epochs=epochs,
            learning_rate=learning_rate,
            progress_callback=progress_callback
        )

        # Save trained model
        model_path = f"models/{model_id}"
        await trainer.save_model(model_path)

        # Update model in database
        await db.update_model_completion(model_id, accuracy, model_path)

        # Reset training state
        training_state["is_training"] = False
        training_state["progress"] = 100

    except Exception as e:
        print(f"Training error: {e}")
        # Update model status to failed
        await db.update_model_status(model_id, "failed")
        training_state["is_training"] = False
        training_state["progress"] = 0

if __name__ == "__main__":
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=4001,
        reload=False
    )
