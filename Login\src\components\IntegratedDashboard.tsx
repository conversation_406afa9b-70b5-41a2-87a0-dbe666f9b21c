import React, { useState, useEffect } from 'react';
import {
  FiMic, <PERSON><PERSON>pload, FiPlay, FiPause, FiStop, FiDownload,
  FiTrendingUp, FiTarget, FiBook, FiUser, FiSettings,
  FiActivity, FiAward, FiClock, FiBarChart3, <PERSON>Eye
} from 'react-icons/fi';
import ASLDashboard from './ASLDashboard';
import './IntegratedDashboard.css';

interface SessionData {
  id: string;
  timestamp: string;
  type: 'speech-to-text' | 'pronunciation' | 'nlp-analysis';
  input: string;
  results: any;
  score?: number;
  duration: number;
}

interface UserProgress {
  totalSessions: number;
  averageScore: number;
  improvementRate: number;
  streakDays: number;
  favoriteTopics: string[];
  weakAreas: string[];
  achievements: string[];
}

const IntegratedDashboard: React.FC = () => {
  const [activeMode, setActiveMode] = useState<'unified' | 'speech' | 'pronunciation' | 'nlp' | 'asl'>('unified');
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentSession, setCurrentSession] = useState<SessionData | null>(null);
  const [sessionHistory, setSessionHistory] = useState<SessionData[]>([]);
  const [userProgress, setUserProgress] = useState<UserProgress | null>(null);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [inputText, setInputText] = useState('');
  const [expectedText, setExpectedText] = useState('');

  useEffect(() => {
    loadUserProgress();
    loadSessionHistory();
  }, []);

  const loadUserProgress = async () => {
    // Mock user progress data - in real app, fetch from backend
    setUserProgress({
      totalSessions: 47,
      averageScore: 87.3,
      improvementRate: 12.5,
      streakDays: 7,
      favoriteTopics: ['Technology', 'Education', 'Science'],
      weakAreas: ['Pronunciation', 'Complex Sentences'],
      achievements: ['First Session', '7-Day Streak', 'High Accuracy', 'NLP Explorer']
    });
  };

  const loadSessionHistory = async () => {
    // Mock session history - in real app, fetch from backend
    const mockSessions: SessionData[] = [
      {
        id: '1',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        type: 'speech-to-text',
        input: 'Audio recording about AI technology',
        results: { transcription: 'Artificial intelligence is transforming education', confidence: 0.92 },
        score: 92,
        duration: 45
      },
      {
        id: '2',
        timestamp: new Date(Date.now() - 7200000).toISOString(),
        type: 'pronunciation',
        input: 'Hello, how are you today?',
        results: { accuracy: 85, feedback: 'Good pronunciation overall' },
        score: 85,
        duration: 30
      }
    ];
    setSessionHistory(mockSessions);
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const recorder = new MediaRecorder(stream);
      const chunks: BlobPart[] = [];

      recorder.ondataavailable = (event) => {
        chunks.push(event.data);
      };

      recorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'audio/wav' });
        setAudioBlob(blob);
        stream.getTracks().forEach(track => track.stop());
      };

      recorder.start();
      setMediaRecorder(recorder);
      setIsRecording(true);
    } catch (error) {
      console.error('Recording error:', error);
    }
  };

  const stopRecording = () => {
    if (mediaRecorder && isRecording) {
      mediaRecorder.stop();
      setMediaRecorder(null);
      setIsRecording(false);
    }
  };

  const processUnifiedAnalysis = async () => {
    if (!audioBlob && !inputText) return;

    setIsProcessing(true);

    try {
      let results: any = {};

      if (audioBlob) {
        // Process audio with speech-to-text + NLP
        const formData = new FormData();
        formData.append('audio', audioBlob, 'recording.wav');
        formData.append('includeNlp', 'true');

        const speechResponse = await fetch('http://localhost:4001/api/custom-transcribe', {
          method: 'POST',
          body: formData,
        });

        if (speechResponse.ok) {
          const speechResult = await speechResponse.json();
          results.transcription = speechResult.text;
          results.nlp_analysis = speechResult.nlp_analysis;

          // If we have expected text, also do pronunciation assessment
          if (expectedText) {
            const pronFormData = new FormData();
            pronFormData.append('audio', audioBlob, 'recording.wav');
            pronFormData.append('expectedText', expectedText);

            const pronResponse = await fetch('http://localhost:4001/api/assess-pronunciation', {
              method: 'POST',
              body: pronFormData,
            });

            if (pronResponse.ok) {
              const pronResult = await pronResponse.json();
              results.pronunciation = pronResult;
            }
          }
        }
      } else if (inputText) {
        // Process text with NLP only
        const nlpResponse = await fetch('http://localhost:4001/api/analyze-text', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ text: inputText, includeAdvanced: true }),
        });

        if (nlpResponse.ok) {
          results.nlp_analysis = await nlpResponse.json();
        }
      }

      // Create session record
      const session: SessionData = {
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        type: 'speech-to-text',
        input: audioBlob ? 'Audio recording' : inputText,
        results,
        score: calculateOverallScore(results),
        duration: 60 // Mock duration
      };

      setCurrentSession(session);
      setSessionHistory(prev => [session, ...prev.slice(0, 9)]); // Keep last 10 sessions

    } catch (error) {
      console.error('Processing error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const calculateOverallScore = (results: any): number => {
    let scores: number[] = [];

    if (results.nlp_analysis?.confidence) {
      scores.push(results.nlp_analysis.confidence * 100);
    }

    if (results.pronunciation?.accuracy) {
      scores.push(results.pronunciation.accuracy);
    }

    if (results.transcription && results.transcription.length > 0) {
      scores.push(85); // Base transcription score
    }

    return scores.length > 0 ? Math.round(scores.reduce((a, b) => a + b, 0) / scores.length) : 0;
  };

  const downloadSession = (session: SessionData) => {
    const data = {
      session_id: session.id,
      timestamp: session.timestamp,
      type: session.type,
      input: session.input,
      results: session.results,
      score: session.score,
      duration: session.duration
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `signify-session-${session.id}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return '#10b981';
    if (score >= 80) return '#f59e0b';
    if (score >= 70) return '#ef4444';
    return '#6b7280';
  };

  return (
    <div className="integrated-dashboard">
      <div className="dashboard-header">
        <h1>🚀 Signify.Ed Unified AI Platform</h1>
        <p>Complete speech analysis, NLP insights, and learning analytics</p>
      </div>

      {/* Mode Selector */}
      <div className="mode-selector">
        <button
          onClick={() => setActiveMode('unified')}
          className={`mode-button ${activeMode === 'unified' ? 'active' : ''}`}
        >
          <FiActivity />
          Unified Analysis
        </button>
        <button
          onClick={() => setActiveMode('speech')}
          className={`mode-button ${activeMode === 'speech' ? 'active' : ''}`}
        >
          <FiMic />
          Speech-to-Text
        </button>
        <button
          onClick={() => setActiveMode('pronunciation')}
          className={`mode-button ${activeMode === 'pronunciation' ? 'active' : ''}`}
        >
          <FiTarget />
          Pronunciation
        </button>
        <button
          onClick={() => setActiveMode('nlp')}
          className={`mode-button ${activeMode === 'nlp' ? 'active' : ''}`}
        >
          <FiBook />
          NLP Analysis
        </button>
        <button
          onClick={() => setActiveMode('asl')}
          className={`mode-button ${activeMode === 'asl' ? 'active' : ''}`}
        >
          <FiEye />
          ASL Animation
        </button>
      </div>

      {/* Conditional rendering based on active mode */}
      {activeMode === 'asl' ? (
        <div style={{ padding: '2rem', textAlign: 'center', background: 'rgba(13, 27, 42, 0.8)', borderRadius: '16px', border: '1px solid rgba(255, 255, 255, 0.1)' }}>
          <h2 style={{ color: '#ffcc00', marginBottom: '1rem' }}>🤟 ASL Animation Studio</h2>
          <p style={{ color: '#e6f1ff', marginBottom: '2rem' }}>Welcome to the ASL Animation Studio! This feature converts text to American Sign Language animations.</p>
          <div style={{ background: 'rgba(255, 204, 0, 0.1)', padding: '1rem', borderRadius: '8px', border: '1px solid rgba(255, 204, 0, 0.3)' }}>
            <p style={{ color: '#ffcc00', margin: 0 }}>🚧 ASL Dashboard is loading... If you see this message, the ASL button is working!</p>
          </div>
          <div style={{ marginTop: '2rem' }}>
            <button
              onClick={() => setActiveMode('unified')}
              style={{
                padding: '0.8rem 1.5rem',
                background: '#ffcc00',
                color: '#0a192f',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontWeight: '600'
              }}
            >
              ← Back to Unified Analysis
            </button>
          </div>
        </div>
      ) : (
        <div className="dashboard-content">
          {/* Left Panel - Input & Controls */}
          <div className="input-panel">
          <div className="input-section">
            <h3>📝 Input</h3>

            {/* Audio Recording */}
            <div className="audio-controls">
              <button
                onClick={isRecording ? stopRecording : startRecording}
                className={`record-button ${isRecording ? 'recording' : ''}`}
                disabled={isProcessing}
              >
                <FiMic />
                {isRecording ? 'Stop Recording' : 'Record Audio'}
              </button>

              {audioBlob && (
                <div className="audio-preview">
                  <FiPlay />
                  <span>Audio recorded ({Math.round(audioBlob.size / 1024)}KB)</span>
                </div>
              )}
            </div>

            {/* Text Input */}
            <div className="text-input-section">
              <textarea
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                placeholder="Or type text for analysis..."
                className="text-input"
                rows={3}
                disabled={isProcessing}
              />
            </div>

            {/* Expected Text for Pronunciation */}
            {(activeMode === 'unified' || activeMode === 'pronunciation') && (
              <div className="expected-text-section">
                <input
                  type="text"
                  value={expectedText}
                  onChange={(e) => setExpectedText(e.target.value)}
                  placeholder="Expected text for pronunciation assessment (optional)"
                  className="expected-text-input"
                  disabled={isProcessing}
                />
              </div>
            )}

            {/* Process Button */}
            <button
              onClick={processUnifiedAnalysis}
              className="process-button"
              disabled={isProcessing || (!audioBlob && !inputText)}
            >
              {isProcessing ? (
                <>
                  <FiClock className="spinning" />
                  Processing...
                </>
              ) : (
                <>
                  <FiActivity />
                  Analyze Everything
                </>
              )}
            </button>
          </div>

          {/* User Progress */}
          {userProgress && (
            <div className="progress-section">
              <h3>📊 Your Progress</h3>
              <div className="progress-stats">
                <div className="stat-item">
                  <FiBarChart3 />
                  <div>
                    <span className="stat-value">{userProgress.totalSessions}</span>
                    <span className="stat-label">Total Sessions</span>
                  </div>
                </div>
                <div className="stat-item">
                  <FiTrendingUp />
                  <div>
                    <span className="stat-value">{userProgress.averageScore}%</span>
                    <span className="stat-label">Average Score</span>
                  </div>
                </div>
                <div className="stat-item">
                  <FiAward />
                  <div>
                    <span className="stat-value">{userProgress.streakDays}</span>
                    <span className="stat-label">Day Streak</span>
                  </div>
                </div>
              </div>

              <div className="achievements">
                <h4>🏆 Achievements</h4>
                <div className="achievement-badges">
                  {userProgress.achievements.map((achievement, index) => (
                    <span key={index} className="achievement-badge">
                      {achievement}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Right Panel - Results & History */}
        <div className="results-panel">
          {/* Current Session Results */}
          {currentSession && (
            <div className="current-results">
              <div className="results-header">
                <h3>🎯 Analysis Results</h3>
                <div className="overall-score" style={{ color: getScoreColor(currentSession.score || 0) }}>
                  {currentSession.score}%
                </div>
              </div>

              <div className="results-content">
                {/* Transcription */}
                {currentSession.results.transcription && (
                  <div className="result-section">
                    <h4>📝 Transcription</h4>
                    <p className="transcription-text">{currentSession.results.transcription}</p>
                  </div>
                )}

                {/* NLP Analysis */}
                {currentSession.results.nlp_analysis && (
                  <div className="result-section">
                    <h4>🧠 NLP Insights</h4>
                    <div className="nlp-summary">
                      <div className="insight-item">
                        <span className="insight-label">Sentiment:</span>
                        <span className={`sentiment-${currentSession.results.nlp_analysis.sentiment?.label}`}>
                          {currentSession.results.nlp_analysis.sentiment?.label}
                        </span>
                      </div>
                      <div className="insight-item">
                        <span className="insight-label">Intent:</span>
                        <span>{currentSession.results.nlp_analysis.intent?.intent}</span>
                      </div>
                      <div className="insight-item">
                        <span className="insight-label">Topics:</span>
                        <span>{currentSession.results.nlp_analysis.topics?.join(', ')}</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Pronunciation Assessment */}
                {currentSession.results.pronunciation && (
                  <div className="result-section">
                    <h4>🗣️ Pronunciation</h4>
                    <div className="pronunciation-summary">
                      <div className="pronunciation-score">
                        Accuracy: {currentSession.results.pronunciation.accuracy}%
                      </div>
                      <p className="pronunciation-feedback">
                        {currentSession.results.pronunciation.feedback}
                      </p>
                    </div>
                  </div>
                )}

                <button
                  onClick={() => downloadSession(currentSession)}
                  className="download-button"
                >
                  <FiDownload />
                  Download Results
                </button>
              </div>
            </div>
          )}

          {/* Session History */}
          <div className="session-history">
            <h3>📚 Recent Sessions</h3>
            <div className="history-list">
              {sessionHistory.map((session) => (
                <div key={session.id} className="history-item">
                  <div className="history-header">
                    <span className="session-type">{session.type}</span>
                    <span className="session-time">{formatTimestamp(session.timestamp)}</span>
                    <span
                      className="session-score"
                      style={{ color: getScoreColor(session.score || 0) }}
                    >
                      {session.score}%
                    </span>
                  </div>
                  <div className="history-content">
                    <p className="session-input">{session.input}</p>
                    <button
                      onClick={() => downloadSession(session)}
                      className="mini-download-button"
                    >
                      <FiDownload />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default IntegratedDashboard;
