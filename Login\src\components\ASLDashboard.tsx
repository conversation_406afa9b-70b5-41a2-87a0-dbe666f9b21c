import React, { useState, useEffect } from 'react';
import { 
  FiType, FiPlay, FiDownload, FiSettings, FiInfo, 
  FiRefreshCw, FiSave, FiShare2, FiEye, FiVolume2 
} from 'react-icons/fi';
import ASLAnimation from './ASLAnimation';
import './ASLDashboard.css';

interface ASLSession {
  id: string;
  text: string;
  timestamp: string;
  animation_data?: any;
  duration: number;
  status: 'completed' | 'processing' | 'failed';
}

const ASLDashboard: React.FC = () => {
  const [inputText, setInputText] = useState('');
  const [currentAnimation, setCurrentAnimation] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [sessions, setSessions] = useState<ASLSession[]>([]);
  const [selectedSession, setSelectedSession] = useState<ASLSession | null>(null);
  const [animationSettings, setAnimationSettings] = useState({
    frame_rate: 24,
    max_frames: 120,
    smoothing: 0.3
  });
  const [showSettings, setShowSettings] = useState(false);
  const [error, setError] = useState<string>('');
  const [aslStatus, setAslStatus] = useState<any>(null);

  // Sample texts for quick testing
  const sampleTexts = [
    "Hello, how are you?",
    "Thank you very much",
    "Nice to meet you",
    "I love you",
    "Good morning",
    "Please help me",
    "I am learning sign language",
    "Have a wonderful day"
  ];

  useEffect(() => {
    checkASLStatus();
    loadSessionHistory();
  }, []);

  const checkASLStatus = async () => {
    try {
      const response = await fetch('http://localhost:4001/api/asl-status');
      if (response.ok) {
        const status = await response.json();
        setAslStatus(status);
      }
    } catch (error) {
      console.error('Failed to check ASL status:', error);
    }
  };

  const loadSessionHistory = () => {
    // Load from localStorage for now
    const saved = localStorage.getItem('asl_sessions');
    if (saved) {
      try {
        setSessions(JSON.parse(saved));
      } catch (error) {
        console.error('Failed to load session history:', error);
      }
    }
  };

  const saveSession = (session: ASLSession) => {
    const updatedSessions = [session, ...sessions.slice(0, 9)]; // Keep last 10
    setSessions(updatedSessions);
    localStorage.setItem('asl_sessions', JSON.stringify(updatedSessions));
  };

  const generateASLAnimation = async () => {
    if (!inputText.trim()) {
      setError('Please enter text to convert to ASL');
      return;
    }

    if (inputText.length > 500) {
      setError('Text is too long. Maximum 500 characters allowed.');
      return;
    }

    setIsGenerating(true);
    setError('');
    setCurrentAnimation('');

    const session: ASLSession = {
      id: Date.now().toString(),
      text: inputText,
      timestamp: new Date().toISOString(),
      duration: 0,
      status: 'processing'
    };

    try {
      const response = await fetch('http://localhost:4001/api/generate-asl', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: inputText,
          animation_settings: animationSettings
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to generate ASL: ${response.statusText}`);
      }

      const animationData = await response.json();
      
      // Update session with results
      session.animation_data = animationData;
      session.status = 'completed';
      session.duration = animationData.total_frames / animationData.frame_rate;

      setCurrentAnimation(inputText);
      setSelectedSession(session);
      saveSession(session);

    } catch (err) {
      console.error('ASL generation error:', err);
      setError(err instanceof Error ? err.message : 'Failed to generate ASL animation');
      
      session.status = 'failed';
      saveSession(session);
    } finally {
      setIsGenerating(false);
    }
  };

  const loadSampleText = (text: string) => {
    setInputText(text);
    setError('');
  };

  const loadSession = (session: ASLSession) => {
    if (session.status === 'completed') {
      setInputText(session.text);
      setCurrentAnimation(session.text);
      setSelectedSession(session);
      setError('');
    }
  };

  const shareAnimation = async () => {
    if (!selectedSession?.animation_data) return;

    try {
      if (navigator.share) {
        await navigator.share({
          title: 'ASL Animation',
          text: `Check out this ASL animation for: "${selectedSession.text}"`,
          url: window.location.href
        });
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(
          `ASL Animation: "${selectedSession.text}" - Generated with Signify.Ed`
        );
        alert('Animation details copied to clipboard!');
      }
    } catch (error) {
      console.error('Failed to share:', error);
    }
  };

  const speakText = () => {
    if (!inputText.trim()) return;

    const utterance = new SpeechSynthesisUtterance(inputText);
    utterance.rate = 0.8;
    utterance.pitch = 1;
    speechSynthesis.speak(utterance);
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#10b981';
      case 'processing': return '#f59e0b';
      case 'failed': return '#ef4444';
      default: return '#6b7280';
    }
  };

  return (
    <div className="asl-dashboard">
      <div className="dashboard-header">
        <h1>🤟 ASL Animation Studio</h1>
        <p>Convert text to American Sign Language animations</p>
        
        {aslStatus && (
          <div className="status-indicator">
            <span className={`status-dot ${aslStatus.status}`}></span>
            ASL Engine: {aslStatus.status}
          </div>
        )}
      </div>

      <div className="dashboard-content">
        {/* Left Panel - Input & Controls */}
        <div className="input-panel">
          <div className="text-input-section">
            <h3>📝 Text Input</h3>
            
            <div className="input-container">
              <textarea
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                placeholder="Enter text to convert to ASL..."
                className="text-input"
                rows={4}
                maxLength={500}
                disabled={isGenerating}
              />
              
              <div className="input-actions">
                <button
                  onClick={speakText}
                  className="action-button"
                  disabled={!inputText.trim() || isGenerating}
                  title="Speak text"
                >
                  <FiVolume2 />
                </button>
                
                <button
                  onClick={() => setInputText('')}
                  className="action-button"
                  disabled={!inputText.trim() || isGenerating}
                  title="Clear text"
                >
                  <FiRefreshCw />
                </button>
              </div>
            </div>

            <div className="character-count">
              {inputText.length}/500 characters
            </div>

            {/* Sample Texts */}
            <div className="sample-texts">
              <h4>Quick Examples:</h4>
              <div className="sample-grid">
                {sampleTexts.map((text, index) => (
                  <button
                    key={index}
                    onClick={() => loadSampleText(text)}
                    className="sample-button"
                    disabled={isGenerating}
                  >
                    {text}
                  </button>
                ))}
              </div>
            </div>

            {/* Generate Button */}
            <button
              onClick={generateASLAnimation}
              className="generate-button"
              disabled={!inputText.trim() || isGenerating}
            >
              {isGenerating ? (
                <>
                  <FiRefreshCw className="spinning" />
                  Generating ASL...
                </>
              ) : (
                <>
                  <FiPlay />
                  Generate ASL Animation
                </>
              )}
            </button>

            {error && (
              <div className="error-message">
                {error}
              </div>
            )}
          </div>

          {/* Animation Settings */}
          <div className="settings-section">
            <div className="settings-header">
              <h3>⚙️ Animation Settings</h3>
              <button
                onClick={() => setShowSettings(!showSettings)}
                className="toggle-button"
              >
                <FiSettings />
              </button>
            </div>

            {showSettings && (
              <div className="settings-content">
                <div className="setting-item">
                  <label>Frame Rate (FPS):</label>
                  <select
                    value={animationSettings.frame_rate}
                    onChange={(e) => setAnimationSettings({
                      ...animationSettings,
                      frame_rate: Number(e.target.value)
                    })}
                  >
                    <option value={12}>12 FPS</option>
                    <option value={24}>24 FPS</option>
                    <option value={30}>30 FPS</option>
                    <option value={60}>60 FPS</option>
                  </select>
                </div>

                <div className="setting-item">
                  <label>Max Frames:</label>
                  <input
                    type="range"
                    min="60"
                    max="300"
                    value={animationSettings.max_frames}
                    onChange={(e) => setAnimationSettings({
                      ...animationSettings,
                      max_frames: Number(e.target.value)
                    })}
                  />
                  <span>{animationSettings.max_frames}</span>
                </div>

                <div className="setting-item">
                  <label>Smoothing:</label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={animationSettings.smoothing}
                    onChange={(e) => setAnimationSettings({
                      ...animationSettings,
                      smoothing: Number(e.target.value)
                    })}
                  />
                  <span>{animationSettings.smoothing}</span>
                </div>
              </div>
            )}
          </div>

          {/* Session History */}
          <div className="history-section">
            <h3>📚 Recent Sessions</h3>
            <div className="session-list">
              {sessions.map((session) => (
                <div
                  key={session.id}
                  className={`session-item ${selectedSession?.id === session.id ? 'active' : ''}`}
                  onClick={() => loadSession(session)}
                >
                  <div className="session-header">
                    <span 
                      className="session-status"
                      style={{ color: getStatusColor(session.status) }}
                    >
                      ●
                    </span>
                    <span className="session-time">
                      {formatTimestamp(session.timestamp)}
                    </span>
                  </div>
                  <div className="session-text">
                    {session.text.substring(0, 50)}
                    {session.text.length > 50 ? '...' : ''}
                  </div>
                  {session.status === 'completed' && (
                    <div className="session-duration">
                      {session.duration.toFixed(1)}s
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Panel - Animation Display */}
        <div className="animation-panel">
          {currentAnimation ? (
            <div className="animation-container">
              <ASLAnimation
                text={currentAnimation}
                autoPlay={true}
                showControls={true}
                onAnimationComplete={() => console.log('Animation completed')}
              />

              {selectedSession && (
                <div className="animation-actions">
                  <button
                    onClick={shareAnimation}
                    className="action-button"
                    title="Share animation"
                  >
                    <FiShare2 />
                    Share
                  </button>

                  <button
                    onClick={() => {
                      // Download functionality would be handled by ASLAnimation component
                    }}
                    className="action-button"
                    title="Download animation"
                  >
                    <FiDownload />
                    Download
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="animation-placeholder">
              <FiEye size={48} />
              <h3>No Animation</h3>
              <p>Enter text and click "Generate ASL Animation" to see the 3D avatar perform sign language</p>
              
              {aslStatus && (
                <div className="feature-info">
                  <h4>Available Features:</h4>
                  <ul>
                    {Object.entries(aslStatus.features).map(([feature, enabled]) => (
                      <li key={feature} className={enabled ? 'enabled' : 'disabled'}>
                        {feature.replace(/_/g, ' ')}: {enabled ? '✅' : '❌'}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ASLDashboard;
