#!/usr/bin/env python3
"""
Test ASL Integration
Quick test to verify the ASL animation system is properly integrated
"""

import requests
import json
import time

def test_backend_asl_endpoint():
    """Test the backend ASL generation endpoint"""
    print("🧪 Testing ASL Backend Integration...")

    # Test ASL status endpoint
    try:
        response = requests.get('http://localhost:4001/api/asl-status', timeout=5)
        if response.status_code == 200:
            status = response.json()
            print("✅ ASL Status Endpoint Working")
            print(f"   Status: {status['status']}")
            print(f"   Features: {list(status['features'].keys())}")
        else:
            print(f"❌ ASL Status Endpoint Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ ASL Status Endpoint Error: {e}")
        return False

    # Test ASL generation endpoint
    test_text = "Hello, how are you?"
    print(f"\n🎭 Testing ASL Generation with: '{test_text}'")

    try:
        payload = {
            "text": test_text,
            "animation_settings": {
                "frame_rate": 24,
                "max_frames": 60,
                "smoothing": 0.3
            }
        }

        response = requests.post(
            'http://localhost:4001/api/generate-asl',
            json=payload,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            print("✅ ASL Generation Successful")
            print(f"   Total Frames: {result.get('total_frames', 'N/A')}")
            print(f"   Frame Rate: {result.get('frame_rate', 'N/A')}")
            print(f"   Bones Animated: {len(result.get('bone_animations', {}))}")
            print(f"   Source Text: {result.get('metadata', {}).get('source_text', 'N/A')}")
            return True
        else:
            print(f"❌ ASL Generation Failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False

    except Exception as e:
        print(f"❌ ASL Generation Error: {e}")
        return False

def test_frontend_components():
    """Test if frontend components are properly structured"""
    print("\n🎨 Testing Frontend Component Structure...")

    # Check if ASL components exist
    components_to_check = [
        'login/src/components/ASLAnimation.tsx',
        'login/src/components/ASLAnimation.css',
        'login/src/components/ASLDashboard.tsx',
        'login/src/components/ASLDashboard.css'
    ]

    all_exist = True
    for component in components_to_check:
        try:
            with open(component, 'r', encoding='utf-8') as f:
                content = f.read()
                if len(content) > 100:  # Basic check for non-empty files
                    print(f"✅ {component} - OK ({len(content)} chars)")
                else:
                    print(f"⚠️  {component} - Too small ({len(content)} chars)")
                    all_exist = False
        except FileNotFoundError:
            print(f"❌ {component} - Not found")
            all_exist = False
        except Exception as e:
            print(f"❌ {component} - Error: {e}")
            all_exist = False

    return all_exist

def test_integration_points():
    """Test integration points between components"""
    print("\n🔗 Testing Integration Points...")

    # Check if IntegratedDashboard imports ASLDashboard
    try:
        with open('login/src/components/IntegratedDashboard.tsx', 'r', encoding='utf-8') as f:
            content = f.read()

        checks = [
            ("ASLDashboard import", "import ASLDashboard from './ASLDashboard'"),
            ("ASL mode in state", "'asl'"),
            ("ASL mode button", "ASL Animation"),
            ("Conditional rendering", "activeMode === 'asl'")
        ]

        all_integrated = True
        for check_name, check_string in checks:
            if check_string in content:
                print(f"✅ {check_name} - Found")
            else:
                print(f"❌ {check_name} - Missing")
                all_integrated = False

        return all_integrated

    except Exception as e:
        print(f"❌ Integration check error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 ASL Integration Test Suite")
    print("=" * 50)

    # Test results
    results = []

    # Test 1: Backend endpoints
    print("\n1️⃣ Backend API Tests")
    backend_ok = test_backend_asl_endpoint()
    results.append(("Backend API", backend_ok))

    # Test 2: Frontend components
    print("\n2️⃣ Frontend Component Tests")
    frontend_ok = test_frontend_components()
    results.append(("Frontend Components", frontend_ok))

    # Test 3: Integration points
    print("\n3️⃣ Integration Tests")
    integration_ok = test_integration_points()
    results.append(("Integration Points", integration_ok))

    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1

    print(f"\n🎯 Overall: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! ASL integration is working correctly.")
        print("\n📋 Next Steps:")
        print("1. Start the frontend: npm run dev")
        print("2. Navigate to the ASL Animation tab")
        print("3. Enter text and generate ASL animations")
        print("4. Enjoy your 3D ASL avatar! 🤟")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
        print("\n🔧 Troubleshooting:")
        if not results[0][1]:  # Backend failed
            print("- Make sure backend server is running: cd backend && npm run dev")
            print("- Check if Python dependencies are installed")
        if not results[1][1]:  # Frontend failed
            print("- Check if all component files were created correctly")
        if not results[2][1]:  # Integration failed
            print("- Check if IntegratedDashboard.tsx was updated correctly")

    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
