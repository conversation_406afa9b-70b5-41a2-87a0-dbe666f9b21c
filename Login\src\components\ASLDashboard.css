/* ASL Dashboard Styles */
.asl-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  color: #e2e8f0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  padding: 20px;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 32px;
  position: relative;
}

.dashboard-header h1 {
  font-size: 2.5rem;
  font-weight: 800;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6, #ec4899);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: glow 3s ease-in-out infinite alternate;
}

.dashboard-header p {
  font-size: 1.1rem;
  color: #94a3b8;
  margin: 0 0 16px 0;
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid #334155;
  border-radius: 20px;
  font-size: 0.9rem;
  backdrop-filter: blur(8px);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-dot.available {
  background: #10b981;
}

.status-dot.unavailable {
  background: #ef4444;
}

@keyframes glow {
  0% { filter: brightness(1) drop-shadow(0 0 5px rgba(59, 130, 246, 0.3)); }
  100% { filter: brightness(1.2) drop-shadow(0 0 20px rgba(139, 92, 246, 0.5)); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Dashboard Content Layout */
.dashboard-content {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 32px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Input Panel */
.input-panel {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.text-input-section,
.settings-section,
.history-section {
  background: rgba(30, 41, 59, 0.6);
  border: 1px solid #334155;
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(10px);
}

.text-input-section h3,
.settings-section h3,
.history-section h3 {
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #f1f5f9;
}

/* Text Input */
.input-container {
  position: relative;
  margin-bottom: 12px;
}

.text-input {
  width: 100%;
  padding: 16px;
  background: #1e293b;
  border: 2px solid #334155;
  border-radius: 12px;
  color: #e2e8f0;
  font-size: 1rem;
  font-family: inherit;
  resize: vertical;
  transition: all 0.2s ease;
}

.text-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.text-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.input-actions {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  gap: 8px;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(51, 65, 85, 0.8);
  border: 1px solid #475569;
  border-radius: 6px;
  color: #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
}

.action-button:hover:not(:disabled) {
  background: rgba(59, 130, 246, 0.2);
  border-color: #3b82f6;
  transform: translateY(-1px);
}

.action-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.character-count {
  font-size: 0.85rem;
  color: #64748b;
  text-align: right;
  margin-bottom: 16px;
}

/* Sample Texts */
.sample-texts h4 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  color: #cbd5e1;
}

.sample-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 20px;
}

.sample-button {
  padding: 8px 12px;
  background: rgba(51, 65, 85, 0.6);
  border: 1px solid #475569;
  border-radius: 8px;
  color: #e2e8f0;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.sample-button:hover:not(:disabled) {
  background: rgba(59, 130, 246, 0.2);
  border-color: #3b82f6;
  transform: translateY(-1px);
}

.sample-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Generate Button */
.generate-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 16px;
}

.generate-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.generate-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Error Message */
.error-message {
  padding: 12px 16px;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid #ef4444;
  border-radius: 8px;
  color: #fca5a5;
  font-size: 0.9rem;
}

/* Settings Section */
.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.toggle-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(51, 65, 85, 0.6);
  border: 1px solid #475569;
  border-radius: 6px;
  color: #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toggle-button:hover {
  background: rgba(59, 130, 246, 0.2);
  border-color: #3b82f6;
}

.settings-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-item label {
  font-size: 0.9rem;
  color: #cbd5e1;
  font-weight: 500;
}

.setting-item select,
.setting-item input[type="range"] {
  padding: 8px 12px;
  background: #1e293b;
  border: 1px solid #334155;
  border-radius: 6px;
  color: #e2e8f0;
  font-size: 0.9rem;
}

.setting-item input[type="range"] {
  padding: 0;
  height: 6px;
  background: #334155;
  outline: none;
  border-radius: 3px;
}

.setting-item input[type="range"]::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: #3b82f6;
  border-radius: 50%;
  cursor: pointer;
}

/* Session History */
.session-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.session-item {
  padding: 12px;
  background: rgba(51, 65, 85, 0.4);
  border: 1px solid #475569;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.session-item:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: #3b82f6;
}

.session-item.active {
  background: rgba(59, 130, 246, 0.2);
  border-color: #3b82f6;
}

.session-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.session-status {
  font-size: 0.8rem;
}

.session-time {
  font-size: 0.75rem;
  color: #94a3b8;
}

.session-text {
  font-size: 0.85rem;
  color: #e2e8f0;
  margin-bottom: 4px;
}

.session-duration {
  font-size: 0.75rem;
  color: #64748b;
  text-align: right;
}

/* Animation Panel */
.animation-panel {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 600px;
}

.animation-container {
  width: 100%;
  max-width: 600px;
}

.animation-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 16px;
}

.animation-actions .action-button {
  width: auto;
  height: auto;
  padding: 8px 16px;
  gap: 6px;
  font-size: 0.9rem;
}

/* Animation Placeholder */
.animation-placeholder {
  text-align: center;
  padding: 60px 40px;
  background: rgba(30, 41, 59, 0.3);
  border: 2px dashed #334155;
  border-radius: 16px;
  color: #94a3b8;
}

.animation-placeholder h3 {
  margin: 16px 0 8px 0;
  font-size: 1.5rem;
  color: #cbd5e1;
}

.animation-placeholder p {
  margin: 0 0 24px 0;
  font-size: 1rem;
  line-height: 1.5;
}

.feature-info {
  text-align: left;
  max-width: 300px;
  margin: 0 auto;
}

.feature-info h4 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  color: #e2e8f0;
}

.feature-info ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-info li {
  padding: 4px 0;
  font-size: 0.9rem;
  display: flex;
  justify-content: space-between;
}

.feature-info li.enabled {
  color: #10b981;
}

.feature-info li.disabled {
  color: #ef4444;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .input-panel {
    order: 2;
  }
  
  .animation-panel {
    order: 1;
  }
}

@media (max-width: 768px) {
  .asl-dashboard {
    padding: 16px;
  }
  
  .dashboard-header h1 {
    font-size: 2rem;
  }
  
  .sample-grid {
    grid-template-columns: 1fr;
  }
  
  .text-input-section,
  .settings-section,
  .history-section {
    padding: 16px;
  }
}
