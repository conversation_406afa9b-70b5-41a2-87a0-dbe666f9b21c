<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ASL Button Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #0a192f;
            color: white;
            padding: 20px;
        }
        .debug-info {
            background: #1a2332;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .button-test {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .test-button {
            padding: 10px 20px;
            border: 2px solid #ffcc00;
            border-radius: 8px;
            background: rgba(255, 204, 0, 0.1);
            color: #ffcc00;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            background: rgba(255, 204, 0, 0.2);
            transform: translateY(-2px);
        }
        .test-button.active {
            background: rgba(255, 204, 0, 0.3);
        }
    </style>
</head>
<body>
    <h1>🔍 ASL Button Debug Test</h1>
    
    <div class="debug-info">
        <h2>Testing Button Visibility</h2>
        <p>This page tests if the ASL button styling and layout work correctly.</p>
    </div>

    <div class="button-test">
        <button class="test-button">
            📊 Unified Analysis
        </button>
        <button class="test-button">
            🎤 Speech-to-Text
        </button>
        <button class="test-button">
            🎯 Pronunciation
        </button>
        <button class="test-button">
            📚 NLP Analysis
        </button>
        <button class="test-button active">
            👁️ ASL Animation
        </button>
    </div>

    <div class="debug-info">
        <h2>Button Test Results</h2>
        <p id="test-results">All buttons should be visible above. The ASL Animation button should be highlighted.</p>
    </div>

    <div class="debug-info">
        <h2>Next Steps</h2>
        <ol>
            <li>If you can see all 5 buttons above, the styling is working</li>
            <li>Check the browser console for any JavaScript errors</li>
            <li>Verify the React app is loading the IntegratedDashboard component</li>
            <li>Check if there are any import errors in the React components</li>
        </ol>
    </div>

    <script>
        // Simple test to verify JavaScript is working
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.test-button');
            console.log('Found', buttons.length, 'test buttons');
            
            buttons.forEach((button, index) => {
                button.addEventListener('click', function() {
                    buttons.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    console.log('Clicked button:', index, this.textContent.trim());
                });
            });
            
            document.getElementById('test-results').innerHTML = 
                `✅ Found ${buttons.length} buttons. JavaScript is working correctly.`;
        });
    </script>
</body>
</html>
