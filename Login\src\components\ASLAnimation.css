/* ASL Animation Component Styles */
.asl-animation-container {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #334155;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  color: #e2e8f0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.animation-header {
  text-align: center;
  margin-bottom: 20px;
}

.animation-header h3 {
  margin: 0 0 8px 0;
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.source-text {
  margin: 0;
  font-style: italic;
  color: #94a3b8;
  font-size: 0.9rem;
}

/* Animation Viewport */
.animation-viewport {
  position: relative;
  background: #1e293b;
  border-radius: 12px;
  border: 2px solid #334155;
  overflow: hidden;
  margin-bottom: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.animation-canvas {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.animation-canvas:hover {
  transform: scale(1.02);
}

.animation-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 20px;
  background: rgba(15, 23, 42, 0.9);
  border-radius: 12px;
  border: 1px solid #334155;
  backdrop-filter: blur(8px);
}

.animation-overlay.error {
  border-color: #ef4444;
  color: #fca5a5;
}

.animation-overlay .spinning {
  animation: spin 1s linear infinite;
  font-size: 1.5rem;
  color: #3b82f6;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Progress Bar */
.progress-container {
  margin-bottom: 20px;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #334155;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  border-radius: 3px;
  transition: width 0.1s ease;
}

.progress-text {
  font-size: 0.85rem;
  color: #94a3b8;
  text-align: center;
  display: block;
}

/* Animation Controls */
.animation-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.playback-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.view-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #334155;
  border: 1px solid #475569;
  border-radius: 8px;
  color: #e2e8f0;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
}

.control-button:hover:not(:disabled) {
  background: #475569;
  border-color: #64748b;
  transform: translateY(-1px);
}

.control-button:active {
  transform: translateY(0);
}

.control-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-button.primary {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border-color: #3b82f6;
}

.control-button.primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  border-color: #2563eb;
}

.control-button.active {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  border-color: #8b5cf6;
}

.speed-control {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
}

.speed-control label {
  color: #94a3b8;
  font-weight: 500;
}

.speed-control select {
  padding: 4px 8px;
  background: #1e293b;
  border: 1px solid #334155;
  border-radius: 6px;
  color: #e2e8f0;
  font-size: 0.85rem;
  cursor: pointer;
}

.speed-control select:focus {
  outline: none;
  border-color: #3b82f6;
}

/* Animation Info */
.animation-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  padding: 16px;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 8px;
  border: 1px solid #334155;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  text-align: center;
}

.info-item span:first-child {
  font-size: 0.8rem;
  color: #94a3b8;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-item span:last-child {
  font-size: 1rem;
  color: #e2e8f0;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .asl-animation-container {
    padding: 16px;
  }
  
  .animation-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .playback-controls,
  .view-controls {
    justify-content: center;
  }
  
  .animation-canvas {
    max-width: 100%;
    height: auto;
  }
  
  .animation-info {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .animation-header h3 {
    font-size: 1.25rem;
  }
  
  .control-button {
    padding: 6px 10px;
    font-size: 0.85rem;
  }
  
  .animation-info {
    grid-template-columns: 1fr;
  }
}

/* Loading Animation */
.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Hover Effects */
.animation-viewport:hover .animation-canvas {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Focus States */
.control-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.speed-control select:focus {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

/* Animation States */
.control-button.playing {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border-color: #ef4444;
}

.control-button.playing:hover:not(:disabled) {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  border-color: #dc2626;
}
