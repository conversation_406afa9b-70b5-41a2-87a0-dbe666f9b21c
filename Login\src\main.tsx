import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import './index.css'
import App from './App.tsx'
import Home from './Home.tsx'
import { ThemeProvider } from './context/ThemeContext'

const rootElement = document.getElementById('root');
if (rootElement) {
  createRoot(rootElement).render(
  <StrictMode>
    <ThemeProvider>
      <Router>
        <Routes>
          <Route path="/" element={<App />} />
          <Route path="/home" element={<Home />} />
          <Route path="/dashboard" element={<Home />} />
        </Routes>
      </Router>
    </ThemeProvider>
  </StrictMode>,
  )
}
